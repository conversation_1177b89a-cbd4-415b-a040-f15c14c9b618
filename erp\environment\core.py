"""
Core Environment class for ERP
"""
import uuid
from typing import Dict, Any, Optional, TYPE_CHECKING

from .cursor import DatabaseCursor
from ..context.manager import ContextManager
from ..logging import get_logger

if TYPE_CHECKING:
    from ..database.memory import AppRegistry


class Environment:
    """
    Odoo-like Environment providing access to cr, uid, context
    """
    
    def __init__(self, cr: DatabaseCursor, uid: int, context: Dict[str, Any] = None,
                 memory_registry: Optional['AppRegistry'] = None):
        if cr is None:
            raise ValueError("Database cursor (cr) is mandatory")
        if uid is None:
            raise ValueError("User ID (uid) is mandatory")
        
        # Generate unique UUID for this environment instance
        self._uuid = str(uuid.uuid4())
        
        self._cr = cr
        self._uid = uid
        self._context = context or {}
        self._models = {}
        self._memory_registry = memory_registry
        
        # Get logger and log environment creation
        self._logger = get_logger(f"{__name__}.Environment")
        
        registry_info = f" with memory registry" if memory_registry else " without memory registry"
        self._logger.info(
            f"Environment created - UUID: {self._uuid}, DB: {cr.db_name}, UID: {uid}, "
            f"Context: {self._context}{registry_info}"
        )
        
        # Also log to console for easy debugging
        registry_status = " [REGISTRY]" if memory_registry else ""
        self._logger.debug(f"Environment UUID: {self._uuid} | DB: {cr.db_name} | UID: {uid}{registry_status}")
    
    @property
    def uuid(self) -> str:
        """Environment UUID"""
        return self._uuid


    
    @property
    def cr(self) -> DatabaseCursor:
        """Database cursor"""
        return self._cr
    
    @property
    def uid(self) -> int:
        """User ID"""
        return self._uid
    
    @property
    def context(self) -> Dict[str, Any]:
        """Context dictionary"""
        return self._context.copy()
    
    @property
    def memory_registry(self) -> Optional['AppRegistry']:
        """Memory registry for this environment's database"""
        return self._memory_registry
    
    @property
    def has_memory_registry(self) -> bool:
        """Check if this environment has a memory registry"""
        return self._memory_registry is not None
    
    def with_context(self, **context) -> 'Environment':
        """Create new environment with updated context"""
        new_context = {**self._context, **context}
        new_env = Environment(self._cr, self._uid, new_context, self._memory_registry)
        self._logger.debug(f"Environment {self._uuid} created child environment {new_env.uuid} with updated context")
        return new_env
    
    def with_user(self, uid: int) -> 'Environment':
        """Create new environment with different user"""
        new_env = Environment(self._cr, uid, self._context, self._memory_registry)
        self._logger.debug(f"Environment {self._uuid} created child environment {new_env.uuid} with different user {uid}")
        return new_env
    

    
    async def ref(self, xml_id: str):
        """Get record by XML ID using raw SQL (independent of AppRegistry)"""
        try:
            from ..data.xmlid_manager import XMLIDManager

            # Create XML ID manager with database manager
            xmlid_manager = XMLIDManager(self.cr._db_manager)

            # Resolve XML ID to get model and record ID
            result = await xmlid_manager.resolve_xmlid(xml_id)
            if not result:
                raise ValueError(f"XML ID not found: {xml_id}")

            model_name = result['model']
            record_id = result['res_id']

            # Try to get the model if available in registry
            try:
                model = self[model_name]
                return await model.browse(record_id)
            except KeyError:
                # Model not available in registry, return raw record data
                self._logger.debug(f"Model {model_name} not available in registry, returning raw data for XML ID: {xml_id}")
                record_data = await xmlid_manager.resolve_xmlid_to_record(xml_id)
                if record_data:
                    # Create a simple record-like object with the data
                    class SimpleRecord:
                        def __init__(self, data):
                            self.id = data.get('id')
                            self.__dict__.update(data)

                    return SimpleRecord(record_data)
                else:
                    raise ValueError(f"Record data not found for XML ID: {xml_id}")

        except Exception as e:
            self._logger.error(f"Error resolving XML ID {xml_id}: {e}")
            raise ValueError(f"Error resolving XML ID {xml_id}: {e}")



    async def __aenter__(self):
        """Async context manager entry"""
        # Set this environment in the context
        ContextManager.set_environment(self)
        ContextManager.set_database(self._cr.db_name)
        ContextManager.set_user(self._uid)
        
        # Register with memory registry if available
        if self._memory_registry:
            await self._memory_registry.register_environment(self)
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        # Context will be automatically reset when the context var goes out of scope
        pass
    
    def __repr__(self):
        return f"Environment(uuid={self._uuid}, cr={self._cr.db_name}, uid={self._uid}, context={self._context})"
