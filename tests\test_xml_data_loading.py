"""
Tests for XML data loading and XML ID resolution functionality

DEPRECATED: This test file uses the old Environment-based approach.
Use test_sql_xml_loading.py and test_sql_xml_integration.py for the new SQL-based approach.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from pathlib import Path
import tempfile
import os

from erp.data.loader import DataLoader
from erp.data.parser import XMLDataParser
from erp.database.connection.manager import DatabaseManager


class TestXMLDataLoading:
    """Test XML data loading with ir.model.data support"""

    @pytest.fixture
    def sample_xml_content(self):
        """Sample XML data for testing"""
        return '''<?xml version="1.0" encoding="utf-8"?>
<data noupdate="1">
    <!-- Test group category -->
    <record id="category_test" model="ir.module.category">
        <field name="name">Test Category</field>
        <field name="sequence">10</field>
    </record>

    <!-- Test group -->
    <record id="group_test" model="res.groups">
        <field name="name">Test Group</field>
        <field name="category_id" ref="category_test"/>
        <field name="comment">Test group for XML loading</field>
    </record>

    <!-- Test user -->
    <record id="user_test" model="res.users">
        <field name="name">Test User</field>
        <field name="login">testuser</field>
        <field name="email"><EMAIL></field>
        <field name="groups_id" eval="[(6, 0, [ref('group_test')])]"/>
    </record>
</data>'''

    @pytest.fixture
    def mock_environment(self):
        """Mock environment for testing"""
        env = MagicMock(spec=Environment)
        
        # Mock ir.model.data model
        ir_model_data = AsyncMock()
        ir_model_data.xmlid_lookup = AsyncMock()
        ir_model_data.create_or_update_xmlid = AsyncMock()
        
        # Mock other models
        category_model = AsyncMock()
        group_model = AsyncMock()
        user_model = AsyncMock()
        
        # Mock model access
        def get_model(model_name):
            if model_name == 'ir.model.data':
                return ir_model_data
            elif model_name == 'ir.module.category':
                return category_model
            elif model_name == 'res.groups':
                return group_model
            elif model_name == 'res.users':
                return user_model
            else:
                raise KeyError(f"Model {model_name} not found")
        
        env.__getitem__ = get_model
        
        return env, ir_model_data, category_model, group_model, user_model

    def test_xml_parser_with_references(self, sample_xml_content):
        """Test XML parsing with references"""
        parser = XMLDataParser()
        records = parser.parse_content(sample_xml_content)
        
        assert len(records) == 3
        
        # Check category record
        category_record = next(r for r in records if r['xml_id'] == 'category_test')
        assert category_record['model'] == 'ir.module.category'
        assert category_record['values']['name']['value'] == 'Test Category'
        assert category_record['noupdate'] == True
        
        # Check group record with reference
        group_record = next(r for r in records if r['xml_id'] == 'group_test')
        assert group_record['model'] == 'res.groups'
        assert group_record['values']['category_id']['type'] == 'ref'
        assert group_record['values']['category_id']['value'] == 'category_test'
        
        # Check user record with eval reference
        user_record = next(r for r in records if r['xml_id'] == 'user_test')
        assert user_record['model'] == 'res.users'
        assert 'groups_id' in user_record['values']

    @pytest.mark.asyncio
    async def test_xml_id_storage_and_lookup(self, sample_xml_content, mock_environment):
        """Test XML ID storage and lookup functionality"""
        env, ir_model_data, category_model, group_model, user_model = mock_environment
        
        # Mock record creation
        category_record = MagicMock()
        category_record.id = 'cat_123'
        category_model.create = AsyncMock(return_value=category_record)
        category_model.browse = AsyncMock(return_value=category_record)
        
        # Mock XML ID lookup - initially not found, then found
        ir_model_data.xmlid_lookup.side_effect = [
            None,  # First lookup (not found)
            {'model': 'ir.module.category', 'res_id': 'cat_123'}  # Second lookup (found)
        ]
        
        # Create data loader
        loader = DataLoader(env)
        
        # Load the XML content
        result = await loader.load_data_content(sample_xml_content, 'base')
        
        # Verify XML ID was stored
        ir_model_data.create_or_update_xmlid.assert_called()
        
        # Check that the XML ID storage was called with correct parameters
        calls = ir_model_data.create_or_update_xmlid.call_args_list
        assert len(calls) >= 1
        
        # Verify the first call was for the category
        first_call = calls[0]
        assert first_call[1]['module'] == 'base'
        assert first_call[1]['name'] == 'category_test'
        assert first_call[1]['model'] == 'ir.module.category'
        assert first_call[1]['res_id'] == 'cat_123'

    @pytest.mark.asyncio
    async def test_xml_reference_resolution(self, mock_environment):
        """Test XML reference resolution"""
        env, ir_model_data, category_model, group_model, user_model = mock_environment
        
        # Mock XML ID lookup to return a reference
        ir_model_data.xmlid_lookup.return_value = {
            'model': 'ir.module.category',
            'res_id': 'cat_123'
        }
        
        # Create data loader
        loader = DataLoader(env)
        
        # Test reference resolution
        resolved_id = await loader._resolve_reference('base.category_test')
        
        # Verify lookup was called
        ir_model_data.xmlid_lookup.assert_called_with('base.category_test')
        
        # Verify resolved ID
        assert resolved_id == 'cat_123'

    @pytest.mark.asyncio
    async def test_environment_ref_method(self, mock_environment):
        """Test Environment.ref() method for XML ID resolution"""
        env, ir_model_data, category_model, group_model, user_model = mock_environment
        
        # Mock XML ID lookup
        ir_model_data.xmlid_lookup.return_value = {
            'model': 'ir.module.category',
            'res_id': 'cat_123'
        }
        
        # Mock record browsing
        category_record = MagicMock()
        category_model.browse.return_value = category_record
        
        # Test ref method
        result = await env.ref('base.category_test')
        
        # Verify lookup was called
        ir_model_data.xmlid_lookup.assert_called_with('base.category_test')
        
        # Verify browse was called
        category_model.browse.assert_called_with('cat_123')
        
        # Verify result
        assert result == category_record

    @pytest.mark.asyncio
    async def test_xml_id_not_found_error(self, mock_environment):
        """Test error handling when XML ID is not found"""
        env, ir_model_data, category_model, group_model, user_model = mock_environment
        
        # Mock XML ID lookup to return None (not found)
        ir_model_data.xmlid_lookup.return_value = None
        
        # Test ref method with non-existent XML ID
        with pytest.raises(ValueError, match="XML ID not found"):
            await env.ref('base.nonexistent_id')

    @pytest.mark.asyncio
    async def test_data_file_loading(self, sample_xml_content, mock_environment):
        """Test loading data from a file"""
        env, ir_model_data, category_model, group_model, user_model = mock_environment
        
        # Mock record creation
        category_record = MagicMock()
        category_record.id = 'cat_123'
        category_model.create = AsyncMock(return_value=category_record)
        
        group_record = MagicMock()
        group_record.id = 'grp_456'
        group_model.create = AsyncMock(return_value=group_record)
        
        user_record = MagicMock()
        user_record.id = 'usr_789'
        user_model.create = AsyncMock(return_value=user_record)
        
        # Mock XML ID lookup - not found initially
        ir_model_data.xmlid_lookup.return_value = None
        
        # Create temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.xml', delete=False) as f:
            f.write(sample_xml_content)
            temp_file = f.name
        
        try:
            # Create data loader
            loader = DataLoader(env)
            
            # Load the file
            result = await loader.load_data_file(temp_file, 'base')
            
            # Verify records were created
            assert result['loaded'] >= 0  # May be 0 due to mocking, but should not error
            assert 'errors' in result
            
        finally:
            # Clean up
            os.unlink(temp_file)

    def test_ir_model_data_model_methods(self):
        """Test ir.model.data model class methods"""
        from addons.base.models.ir_model_data import IrModelData
        
        # Test compute_complete_name
        record = MagicMock()
        record.module = 'base'
        record.name = 'test_id'
        
        # Create instance and test compute method
        ir_data = IrModelData()
        ir_data._compute_complete_name.im_func(record)
        
        # Verify complete name was set
        assert record.complete_name == 'base.test_id'
