"""
Tests for SQL-based XML loading system

This test suite verifies that the new SQL-based XML loading system
works correctly and is completely independent of AppRegistry.
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from erp.data.sql_helpers import SQLHelpers, XMLIDSQLHelpers, ModelSQLHelpers
from erp.data.xmlid_manager import XMLIDManager
from erp.data.loader import DataLoader
from erp.database.connection.manager import DatabaseManager


class TestSQLHelpers:
    """Test the base SQL helper utilities"""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager for testing"""
        db_manager = AsyncMock(spec=DatabaseManager)
        return db_manager
    
    @pytest.fixture
    def sql_helpers(self, mock_db_manager):
        """Create SQL helpers instance for testing"""
        return SQLHelpers(mock_db_manager)
    
    @pytest.mark.asyncio
    async def test_table_exists(self, sql_helpers, mock_db_manager):
        """Test table existence check"""
        mock_db_manager.fetchval.return_value = True
        
        result = await sql_helpers.table_exists('test_table')
        
        assert result is True
        mock_db_manager.fetchval.assert_called_once()
        call_args = mock_db_manager.fetchval.call_args
        assert 'test_table' in call_args[0]
    
    @pytest.mark.asyncio
    async def test_get_table_columns(self, sql_helpers, mock_db_manager):
        """Test getting table columns"""
        mock_rows = [
            {'column_name': 'id'},
            {'column_name': 'name'},
            {'column_name': 'create_date'}
        ]
        mock_db_manager.fetch.return_value = mock_rows
        
        columns = await sql_helpers.get_table_columns('test_table')
        
        assert columns == ['id', 'name', 'create_date']
        mock_db_manager.fetch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_insert_record(self, sql_helpers, mock_db_manager):
        """Test record insertion"""
        mock_db_manager.fetchval.return_value = 'test-id-123'
        mock_db_manager.fetch.return_value = [
            {'column_name': 'id'},
            {'column_name': 'name'},
            {'column_name': 'create_date'}
        ]
        
        data = {'name': 'Test Record'}
        result = await sql_helpers.insert_record('test_table', data)
        
        assert result == 'test-id-123'
        mock_db_manager.fetchval.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_find_record(self, sql_helpers, mock_db_manager):
        """Test finding a record"""
        mock_row = {'id': 'test-id', 'name': 'Test Record'}
        mock_db_manager.fetchrow.return_value = mock_row
        
        conditions = {'name': 'Test Record'}
        result = await sql_helpers.find_record('test_table', conditions)
        
        assert result == mock_row
        mock_db_manager.fetchrow.assert_called_once()


class TestXMLIDSQLHelpers:
    """Test XML ID specific SQL helpers"""
    
    @pytest.fixture
    def mock_sql_helpers(self):
        """Mock SQL helpers for testing"""
        sql_helpers = AsyncMock(spec=SQLHelpers)
        sql_helpers.table_exists.return_value = True
        return sql_helpers
    
    @pytest.fixture
    def xmlid_sql_helpers(self, mock_sql_helpers):
        """Create XML ID SQL helpers instance for testing"""
        return XMLIDSQLHelpers(mock_sql_helpers)
    
    @pytest.mark.asyncio
    async def test_xmlid_lookup_with_module(self, xmlid_sql_helpers, mock_sql_helpers):
        """Test XML ID lookup with module prefix"""
        mock_record = {
            'model': 'test.model',
            'res_id': 'record-123',
            'module': 'test_module',
            'name': 'test_record'
        }
        mock_sql_helpers.find_record.return_value = mock_record
        
        result = await xmlid_sql_helpers.xmlid_lookup('test_module.test_record')
        
        assert result == {
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        }
        mock_sql_helpers.find_record.assert_called_once_with(
            'ir_model_data',
            {'module': 'test_module', 'name': 'test_record'}
        )
    
    @pytest.mark.asyncio
    async def test_xmlid_lookup_without_module(self, xmlid_sql_helpers, mock_sql_helpers):
        """Test XML ID lookup without module prefix"""
        mock_record = {
            'model': 'test.model',
            'res_id': 'record-123',
            'module': 'base',
            'name': 'test_record'
        }
        mock_sql_helpers.find_record.return_value = mock_record
        
        result = await xmlid_sql_helpers.xmlid_lookup('test_record')
        
        assert result is not None
        mock_sql_helpers.find_record.assert_called_once_with(
            'ir_model_data',
            {'name': 'test_record'}
        )
    
    @pytest.mark.asyncio
    async def test_create_xmlid_mapping(self, xmlid_sql_helpers, mock_sql_helpers):
        """Test creating XML ID mapping"""
        mock_sql_helpers.find_record.return_value = None  # No existing record
        mock_sql_helpers.insert_record.return_value = 'mapping-id-123'
        
        result = await xmlid_sql_helpers.create_or_update_xmlid(
            'test_module', 'test_record', 'test.model', 'record-123'
        )
        
        assert result is True
        mock_sql_helpers.insert_record.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_existing_xmlid_mapping(self, xmlid_sql_helpers, mock_sql_helpers):
        """Test updating existing XML ID mapping"""
        existing_record = {
            'id': 'existing-id',
            'noupdate': False,
            'model': 'old.model',
            'res_id': 'old-record-id'
        }
        mock_sql_helpers.find_record.return_value = existing_record
        mock_sql_helpers.update_record.return_value = True
        
        result = await xmlid_sql_helpers.create_or_update_xmlid(
            'test_module', 'test_record', 'new.model', 'new-record-123'
        )
        
        assert result is True
        mock_sql_helpers.update_record.assert_called_once()


class TestXMLIDManager:
    """Test the XML ID Manager"""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager for testing"""
        return AsyncMock(spec=DatabaseManager)
    
    @pytest.fixture
    def xmlid_manager(self, mock_db_manager):
        """Create XML ID manager instance for testing"""
        return XMLIDManager(mock_db_manager)
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid(self, xmlid_manager):
        """Test XML ID resolution"""
        # Mock the xmlid_sql.xmlid_lookup method
        xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        })
        
        result = await xmlid_manager.resolve_xmlid('test_module.test_record')
        
        assert result == {
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        }
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid_to_record_id(self, xmlid_manager):
        """Test XML ID resolution to record ID only"""
        xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        })
        
        result = await xmlid_manager.resolve_xmlid_to_record_id('test_module.test_record')
        
        assert result == 'record-123'
    
    @pytest.mark.asyncio
    async def test_resolve_xmlid_not_found(self, xmlid_manager):
        """Test XML ID resolution when not found"""
        xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value=None)
        
        result = await xmlid_manager.resolve_xmlid('nonexistent.record')
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_create_xmlid_mapping(self, xmlid_manager):
        """Test creating XML ID mapping"""
        xmlid_manager.xmlid_sql.create_or_update_xmlid = AsyncMock(return_value=True)
        
        result = await xmlid_manager.create_xmlid_mapping(
            'test_module', 'test_record', 'test.model', 'record-123'
        )
        
        assert result is True
        xmlid_manager.xmlid_sql.create_or_update_xmlid.assert_called_once_with(
            'test_module', 'test_record', 'test.model', 'record-123', False
        )
    
    @pytest.mark.asyncio
    async def test_caching_functionality(self, xmlid_manager):
        """Test XML ID caching functionality"""
        xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        })
        
        # First call should hit the database
        result1 = await xmlid_manager.resolve_xmlid('test_module.test_record')
        
        # Second call should use cache
        result2 = await xmlid_manager.resolve_xmlid('test_module.test_record')
        
        assert result1 == result2
        # Should only be called once due to caching
        xmlid_manager.xmlid_sql.xmlid_lookup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cache_disable(self, xmlid_manager):
        """Test disabling cache functionality"""
        xmlid_manager.xmlid_sql.xmlid_lookup = AsyncMock(return_value={
            'model': 'test.model',
            'res_id': 'record-123',
            'id': 'record-123'
        })
        
        # Disable cache
        xmlid_manager.enable_cache(False)
        
        # Both calls should hit the database
        await xmlid_manager.resolve_xmlid('test_module.test_record')
        await xmlid_manager.resolve_xmlid('test_module.test_record')
        
        # Should be called twice since cache is disabled
        assert xmlid_manager.xmlid_sql.xmlid_lookup.call_count == 2


class TestDataLoaderSQL:
    """Test the SQL-based DataLoader"""
    
    @pytest.fixture
    def mock_db_manager(self):
        """Mock database manager for testing"""
        return AsyncMock(spec=DatabaseManager)
    
    @pytest.fixture
    def data_loader(self, mock_db_manager):
        """Create DataLoader instance for testing"""
        return DataLoader(mock_db_manager)
    
    @pytest.mark.asyncio
    async def test_load_single_record_create(self, data_loader):
        """Test loading a single record (create scenario)"""
        # Mock dependencies
        data_loader.sql.table_exists = AsyncMock(return_value=True)
        data_loader.xmlid_manager.resolve_xmlid_to_record_id = AsyncMock(return_value=None)
        data_loader.model_sql.create_record = AsyncMock(return_value='new-record-123')
        data_loader.xmlid_manager.create_xmlid_mapping = AsyncMock(return_value=True)
        
        record_def = {
            'model': 'test.model',
            'xml_id': 'test_record',
            'values': {'name': 'Test Record'},
            'noupdate': False
        }
        
        result = await data_loader._load_single_record(record_def, 'test_module')
        
        assert result is True
        data_loader.model_sql.create_record.assert_called_once()
        data_loader.xmlid_manager.create_xmlid_mapping.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_single_record_update(self, data_loader):
        """Test loading a single record (update scenario)"""
        # Mock dependencies
        data_loader.sql.table_exists = AsyncMock(return_value=True)
        data_loader.xmlid_manager.resolve_xmlid_to_record_id = AsyncMock(return_value='existing-record-123')
        data_loader.model_sql.update_record = AsyncMock(return_value=True)
        
        record_def = {
            'model': 'test.model',
            'xml_id': 'test_record',
            'values': {'name': 'Updated Test Record'},
            'noupdate': False
        }
        
        result = await data_loader._load_single_record(record_def, 'test_module')
        
        assert result is True
        data_loader.model_sql.update_record.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_resolve_reference(self, data_loader):
        """Test resolving XML ID references"""
        data_loader.xmlid_manager.resolve_xmlid_to_record_id = AsyncMock(return_value='referenced-record-123')
        
        result = await data_loader._resolve_reference('test_module.referenced_record')
        
        assert result == 'referenced-record-123'
        data_loader.xmlid_manager.resolve_xmlid_to_record_id.assert_called_once_with('test_module.referenced_record')
    
    @pytest.mark.asyncio
    async def test_model_not_found(self, data_loader):
        """Test handling when model table doesn't exist"""
        data_loader.sql.table_exists = AsyncMock(return_value=False)
        
        record_def = {
            'model': 'nonexistent.model',
            'xml_id': 'test_record',
            'values': {'name': 'Test Record'},
            'noupdate': False
        }
        
        with pytest.raises(Exception):  # Should raise ModelNotFoundError
            await data_loader._load_single_record(record_def, 'test_module')
