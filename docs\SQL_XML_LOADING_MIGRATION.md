# SQL-Based XML Loading Migration

## Overview

This document describes the migration from AppRegistry-dependent XML loading to a completely independent SQL-based XML loading system. The new system removes all `env[modelname]` access and uses raw SQL operations for maximum independence.

## Changes Made

### 1. New SQL Helper Utilities

**File: `erp/data/sql_helpers.py`**
- `SQLHelpers`: Base SQL operations (CRUD, table management)
- `XMLIDSQLHelpers`: XML ID specific operations
- `ModelSQLHelpers`: Model operations without AppRegistry dependency

Key features:
- Direct database access using raw SQL
- Table existence checks
- Record creation, updating, and querying
- Column introspection
- Error handling and logging

### 2. XML ID Manager

**File: `erp/data/xmlid_manager.py`**
- `XMLIDManager`: Complete XML ID management without AppRegistry
- XML ID resolution and creation
- Caching for performance
- Orphaned XML ID cleanup
- Integrity validation

Key features:
- Independent of model registry
- Built-in caching with enable/disable functionality
- Comprehensive XML ID operations
- Validation and cleanup utilities

### 3. Refactored DataLoader

**File: `erp/data/loader.py`** (Modified)
- Removed all `env[modelname]` access
- Uses `XMLIDManager` for XML ID operations
- Uses `ModelSQLHelpers` for record operations
- Direct SQL table existence checks

Key changes:
- Constructor now takes `DatabaseManager` instead of `Environment`
- `_get_model()` replaced with `_model_exists()`
- `_resolve_reference()` uses `XMLIDManager`
- `_store_xml_id_mapping()` uses `XMLIDManager`
- `_find_record_by_xml_id()` replaced with `_find_record_id_by_xml_id()`

### 4. Updated XML Data Loading Components

**File: `erp/addons/installers/components/xml_data_loader.py`** (Modified)
- `load_addon_data_files()` now takes `DatabaseManager` instead of `Environment`
- Updated to work with new SQL-based `DataLoader`

**File: `erp/addons/installers/base_installer.py`** (Modified)
- Updated to pass `db_manager` to XML loader instead of `env`

### 5. Updated Environment.ref() Method

**File: `erp/environment/core.py`** (Modified)
- `ref()` method now uses `XMLIDManager` for XML ID resolution
- Falls back to raw record data when model not available in registry
- Completely independent of AppRegistry for XML ID resolution

### 6. Comprehensive Test Suite

**New Files:**
- `tests/test_sql_xml_loading.py`: Unit tests for SQL-based components
- `tests/test_sql_xml_integration.py`: Integration tests for complete workflow

**Updated Files:**
- `tests/test_xml_data_loading.py`: Marked as deprecated
- `tests/test_xml_integration.py`: Marked as deprecated

## Benefits

### 1. Complete AppRegistry Independence
- XML loading works without any model registry
- Can load XML data during bootstrap phase
- No circular dependency issues

### 2. Performance Improvements
- Direct SQL operations are faster
- Built-in caching for XML ID resolution
- Reduced memory overhead

### 3. Better Error Handling
- Clear error messages for missing tables
- Graceful handling of missing models
- Comprehensive validation utilities

### 4. Maintainability
- Modular design with clear separation of concerns
- Reusable SQL helpers
- Comprehensive test coverage

## Migration Guide

### For Developers

1. **DataLoader Usage:**
   ```python
   # Old way
   loader = DataLoader(env)
   
   # New way
   loader = DataLoader(db_manager)
   ```

2. **XML Data Loading:**
   ```python
   # Old way
   xml_loader = XMLDataLoader()
   result = await xml_loader.load_addon_data_files(env, 'addon_name')
   
   # New way
   xml_loader = XMLDataLoader()
   result = await xml_loader.load_addon_data_files(db_manager, 'addon_name')
   ```

3. **XML ID Operations:**
   ```python
   # Old way (through env)
   record = await env.ref('module.xml_id')
   
   # New way (still works, but now uses SQL)
   record = await env.ref('module.xml_id')
   
   # Direct XML ID manager usage
   xmlid_manager = XMLIDManager(db_manager)
   result = await xmlid_manager.resolve_xmlid('module.xml_id')
   ```

### For Testing

1. **Use New Test Files:**
   - Use `test_sql_xml_loading.py` for unit tests
   - Use `test_sql_xml_integration.py` for integration tests
   - Old test files are marked as deprecated

2. **Mock Database Manager:**
   ```python
   mock_db_manager = AsyncMock(spec=DatabaseManager)
   loader = DataLoader(mock_db_manager)
   ```

## Backward Compatibility

- `Environment.ref()` method still works but now uses SQL internally
- Existing XML data files continue to work without changes
- API signatures remain the same where possible

## Performance Considerations

1. **Caching:**
   - XML ID resolution is cached by default
   - Cache can be disabled for testing: `xmlid_manager.enable_cache(False)`
   - Cache can be cleared: `xmlid_manager.clear_cache()`

2. **Database Connections:**
   - Uses connection pooling from `DatabaseManager`
   - Efficient SQL queries with proper indexing
   - Minimal database round trips

## Future Enhancements

1. **Batch Operations:**
   - Implement batch XML ID creation/updates
   - Bulk record operations

2. **Advanced Caching:**
   - TTL-based cache expiration
   - Cache invalidation strategies

3. **Performance Monitoring:**
   - Query performance metrics
   - XML loading performance tracking

## Troubleshooting

### Common Issues

1. **Table Not Found:**
   - Ensure model tables exist before loading XML data
   - Check table naming convention (dots replaced with underscores)

2. **XML ID Not Found:**
   - Verify XML ID exists in `ir_model_data` table
   - Check module name and XML ID name

3. **Reference Resolution Fails:**
   - Ensure referenced records are loaded first
   - Check XML ID format (module.name)

### Debug Tools

1. **XML ID Validation:**
   ```python
   xmlid_manager = XMLIDManager(db_manager)
   results = await xmlid_manager.validate_xmlid_integrity()
   ```

2. **Orphaned XML ID Cleanup:**
   ```python
   count = await xmlid_manager.cleanup_orphaned_xmlids()
   ```

3. **Cache Statistics:**
   ```python
   # Check cache contents
   print(xmlid_manager._xmlid_cache)
   ```

## Conclusion

The migration to SQL-based XML loading provides a robust, independent, and performant solution for XML data loading in the ERP system. The new architecture eliminates AppRegistry dependencies while maintaining full backward compatibility and improving overall system reliability.
