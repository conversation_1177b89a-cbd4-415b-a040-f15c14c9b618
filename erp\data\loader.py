"""
Data Loader for ERP system

Loads parsed XML data into the database, handling record creation,
updates, and XML ID management using raw SQL operations.
This implementation is completely independent of AppRegistry.
"""

import asyncio
from typing import Dict, List, Any, Optional
from pathlib import Path
import json

from .parser import XMLDataParser
from .exceptions import DataLoadingError, ModelNotFoundError, RecordCreationError, XMLIDError
from .xmlid_manager import XMLIDManager
from .sql_helpers import SQLHelpers, ModelSQLHelpers
from ..logging import get_logger
from ..database.connection.manager import DatabaseManager


class DataLoader:
    """
    Loads XML data into the database using raw SQL operations.

    This implementation is completely independent of AppRegistry and
    uses only raw SQL queries for all database operations.
    """

    def __init__(self, db_manager: DatabaseManager):
        """
        Initialize DataLoader with database manager.

        Args:
            db_manager: Database manager instance for raw SQL operations
        """
        self.db_manager = db_manager
        self.parser = XMLDataParser()
        self.logger = get_logger(__name__)

        # Initialize SQL helpers
        self.sql = SQLHelpers(db_manager)
        self.model_sql = ModelSQLHelpers(self.sql)
        self.xmlid_manager = XMLIDManager(db_manager)
        
    async def load_data_file(self, file_path: str, addon_name: str = None) -> Dict[str, Any]:
        """
        Load data from an XML file
        
        Args:
            file_path: Path to the XML data file
            addon_name: Name of the addon (for XML ID namespacing)
            
        Returns:
            Dictionary with loading results
        """
        try:
            self.logger.info(f"Loading data file: {file_path}")
            
            # Parse the XML file
            records = self.parser.parse_file(file_path)
            
            # Load records into database
            result = await self._load_records(records, addon_name)
            
            self.logger.info(f"Successfully loaded {result['loaded']} records from {file_path}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to load data file {file_path}: {e}")
            raise DataLoadingError(f"Failed to load data file {file_path}: {e}")
    
    async def load_data_content(self, xml_content: str, addon_name: str = None) -> Dict[str, Any]:
        """
        Load data from XML content string
        
        Args:
            xml_content: XML content as string
            addon_name: Name of the addon (for XML ID namespacing)
            
        Returns:
            Dictionary with loading results
        """
        try:
            # Parse the XML content
            records = self.parser.parse_content(xml_content)
            
            # Load records into database
            return await self._load_records(records, addon_name)
            
        except Exception as e:
            self.logger.error(f"Failed to load XML content: {e}")
            raise DataLoadingError(f"Failed to load XML content: {e}")
    
    async def _load_records(self, records: List[Dict[str, Any]], addon_name: str = None) -> Dict[str, Any]:
        """Load a list of record definitions into the database"""
        result = {
            'loaded': 0,
            'updated': 0,
            'skipped': 0,
            'errors': []
        }
        
        for record_def in records:
            try:
                success = await self._load_single_record(record_def, addon_name)
                if success:
                    result['loaded'] += 1
                else:
                    result['skipped'] += 1
                    
            except Exception as e:
                error_msg = f"Failed to load record {record_def.get('xml_id', 'unknown')}: {e}"
                result['errors'].append(error_msg)
                self.logger.error(error_msg)
                
        return result
    
    async def _load_single_record(self, record_def: Dict[str, Any], addon_name: str = None) -> bool:
        """Load a single record into the database using raw SQL"""
        model_name = record_def['model']
        xml_id = record_def.get('xml_id')
        values = record_def['values']
        noupdate = record_def.get('noupdate', False)

        # Check if model table exists
        table_name = model_name.replace('.', '_')
        if not await self.sql.table_exists(table_name):
            raise ModelNotFoundError(f"Table {table_name} for model {model_name} not found")

        # Process field values
        processed_values = await self._process_field_values(values, model_name)

        # Check if record already exists (by XML ID)
        existing_record_id = None
        if xml_id:
            existing_record_id = await self._find_record_id_by_xml_id(xml_id, addon_name)

        if existing_record_id:
            if not noupdate:
                # Update existing record
                success = await self.model_sql.update_record(model_name, existing_record_id, processed_values)
                if success:
                    self.logger.debug(f"Updated record {xml_id} in {model_name}")
                return success
            else:
                self.logger.debug(f"Skipped updating record {xml_id} (noupdate=True)")
                return True
        else:
            # Create new record
            new_record_id = await self.model_sql.create_record(model_name, processed_values)
            if not new_record_id:
                raise RecordCreationError(f"Failed to create record in {model_name}")

            # Store XML ID mapping if provided
            if xml_id:
                await self._store_xml_id_mapping(xml_id, addon_name, model_name, new_record_id)

            self.logger.debug(f"Created record {xml_id or 'no-id'} in {model_name} with ID {new_record_id}")
            return True
    
    async def _process_field_values(self, values: Dict[str, Any], model_name: str) -> Dict[str, Any]:
        """Process field values, handling references and evaluations using raw SQL"""
        processed = {}

        for field_name, field_def in values.items():
            if isinstance(field_def, dict):
                field_type = field_def.get('type')
                field_value = field_def.get('value')

                if field_type == 'ref':
                    # Reference to another record
                    resolved_id = await self._resolve_reference(field_value)
                    processed[field_name] = resolved_id
                elif field_type == 'eval':
                    # Python expression to evaluate
                    processed[field_name] = self._evaluate_expression(field_value)
                else:
                    # Text value
                    processed[field_name] = field_value
            else:
                # Direct value
                processed[field_name] = field_def

        return processed
    
    async def _resolve_reference(self, ref_value: str) -> Optional[str]:
        """Resolve a reference to another record using raw SQL"""
        try:
            # Use XML ID manager to resolve the reference
            record_id = await self.xmlid_manager.resolve_xmlid_to_record_id(ref_value)
            if record_id:
                return record_id

            self.logger.debug(f"Reference not found: {ref_value}")
            return None

        except Exception as e:
            self.logger.debug(f"Failed to resolve reference {ref_value}: {e}")
            return None
    
    def _evaluate_expression(self, expression: str) -> Any:
        """Safely evaluate a Python expression"""
        # For now, handle simple cases
        # In production, this should use a safe evaluation context
        try:
            # Handle common cases
            if expression == 'True':
                return True
            elif expression == 'False':
                return False
            elif expression == 'None':
                return None
            elif expression.startswith("'") and expression.endswith("'"):
                return expression[1:-1]  # String literal
            elif expression.startswith('"') and expression.endswith('"'):
                return expression[1:-1]  # String literal
            else:
                # Try to evaluate as number
                try:
                    return int(expression)
                except ValueError:
                    try:
                        return float(expression)
                    except ValueError:
                        return expression  # Return as string
        except Exception:
            return expression
    
    async def _model_exists(self, model_name: str) -> bool:
        """Check if a model table exists using raw SQL"""
        table_name = model_name.replace('.', '_')
        return await self.sql.table_exists(table_name)
    
    async def _find_record_id_by_xml_id(self, xml_id: str, addon_name: str = None) -> Optional[str]:
        """Find a record ID by its XML ID using raw SQL"""
        try:
            # Build full XML ID
            if addon_name and '.' not in xml_id:
                full_xml_id = f"{addon_name}.{xml_id}"
            else:
                full_xml_id = xml_id

            # Use XML ID manager to resolve
            record_id = await self.xmlid_manager.resolve_xmlid_to_record_id(full_xml_id)
            return record_id

        except Exception as e:
            self.logger.debug(f"Failed to find record by XML ID {xml_id}: {e}")
            return None
    
    async def _store_xml_id_mapping(self, xml_id: str, addon_name: str, model_name: str, record_id: str):
        """Store XML ID to record ID mapping using raw SQL"""
        try:
            # Use addon_name or default to 'base' if not provided
            module = addon_name or 'base'

            # Create or update the XML ID mapping using XML ID manager
            success = await self.xmlid_manager.create_xmlid_mapping(
                module=module,
                name=xml_id,
                model=model_name,
                res_id=str(record_id),
                noupdate=False
            )

            if success:
                self.logger.debug(f"Stored XML ID mapping: {module}.{xml_id} -> {model_name}({record_id})")
            else:
                self.logger.error(f"Failed to store XML ID mapping for {xml_id}")

        except Exception as e:
            self.logger.error(f"Failed to store XML ID mapping for {xml_id}: {e}")
