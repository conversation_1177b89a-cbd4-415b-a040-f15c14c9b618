"""
Integration tests for XML data loading and ir.model.data functionality

DEPRECATED: This test file uses the old Environment-based approach.
Use test_sql_xml_integration.py for the new SQL-based approach that is independent of AppRegistry.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import tempfile
import os

from erp.data.loader import DataLoader
from erp.database.connection.manager import DatabaseManager


class TestXMLIntegration:
    """Integration tests for complete XML data loading flow"""

    @pytest.fixture
    def simple_xml_data(self):
        """Simple XML data for integration testing"""
        return '''<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="test_category" model="ir.module.category">
        <field name="name">Test Integration Category</field>
        <field name="sequence">100</field>
    </record>
    
    <record id="test_group" model="res.groups">
        <field name="name">Test Integration Group</field>
        <field name="category_id" ref="test_category"/>
    </record>
</data>'''

    @pytest.mark.asyncio
    async def test_complete_xml_loading_flow(self, simple_xml_data):
        """Test the complete XML loading flow with ir.model.data"""
        
        # Mock environment and models
        env = MagicMock(spec=Environment)
        
        # Mock ir.model.data
        ir_model_data = AsyncMock()
        ir_model_data.xmlid_lookup = AsyncMock()
        ir_model_data.create_or_update_xmlid = AsyncMock()
        
        # Mock target models
        category_model = AsyncMock()
        group_model = AsyncMock()
        
        # Mock record creation
        category_record = MagicMock()
        category_record.id = 'category_123'
        category_model.create = AsyncMock(return_value=category_record)
        category_model.browse = AsyncMock(return_value=category_record)
        
        group_record = MagicMock()
        group_record.id = 'group_456'
        group_model.create = AsyncMock(return_value=group_record)
        group_model.browse = AsyncMock(return_value=group_record)
        
        # Setup model access
        def get_model(model_name):
            if model_name == 'ir.model.data':
                return ir_model_data
            elif model_name == 'ir.module.category':
                return category_model
            elif model_name == 'res.groups':
                return group_model
            else:
                raise KeyError(f"Model {model_name} not found")
        
        env.__getitem__ = get_model
        
        # Setup XML ID lookup behavior
        def xmlid_lookup_side_effect(xml_id):
            if xml_id == 'base.test_category':
                return {'model': 'ir.module.category', 'res_id': 'category_123'}
            elif xml_id == 'test_category':
                return {'model': 'ir.module.category', 'res_id': 'category_123'}
            return None
        
        ir_model_data.xmlid_lookup.side_effect = xmlid_lookup_side_effect
        
        # Create data loader
        loader = DataLoader(env)
        
        # Load XML data
        result = await loader.load_data_content(simple_xml_data, 'base')
        
        # Verify results
        assert 'loaded' in result
        assert 'errors' in result
        
        # Verify XML ID mappings were created
        assert ir_model_data.create_or_update_xmlid.call_count >= 1
        
        # Verify the calls included our test records
        calls = ir_model_data.create_or_update_xmlid.call_args_list
        
        # Check that category XML ID was stored
        category_call = None
        for call in calls:
            if call[1].get('name') == 'test_category':
                category_call = call
                break
        
        assert category_call is not None
        assert category_call[1]['module'] == 'base'
        assert category_call[1]['model'] == 'ir.module.category'
        assert category_call[1]['res_id'] == 'category_123'

    @pytest.mark.asyncio
    async def test_xml_reference_resolution_flow(self, simple_xml_data):
        """Test XML reference resolution in the complete flow"""
        
        # Mock environment
        env = MagicMock(spec=Environment)
        
        # Mock ir.model.data
        ir_model_data = AsyncMock()
        
        # Mock models
        category_model = AsyncMock()
        group_model = AsyncMock()
        
        # Mock records
        category_record = MagicMock()
        category_record.id = 'category_123'
        category_model.create = AsyncMock(return_value=category_record)
        category_model.browse = AsyncMock(return_value=category_record)
        
        group_record = MagicMock()
        group_record.id = 'group_456'
        group_model.create = AsyncMock(return_value=group_record)
        
        # Setup model access
        def get_model(model_name):
            if model_name == 'ir.model.data':
                return ir_model_data
            elif model_name == 'ir.module.category':
                return category_model
            elif model_name == 'res.groups':
                return group_model
            else:
                raise KeyError(f"Model {model_name} not found")
        
        env.__getitem__ = get_model
        
        # Setup XML ID lookup to simulate finding the category reference
        lookup_calls = []
        def xmlid_lookup_side_effect(xml_id):
            lookup_calls.append(xml_id)
            if xml_id == 'test_category':
                return {'model': 'ir.module.category', 'res_id': 'category_123'}
            return None
        
        ir_model_data.xmlid_lookup.side_effect = xmlid_lookup_side_effect
        ir_model_data.create_or_update_xmlid = AsyncMock()
        
        # Create data loader
        loader = DataLoader(env)
        
        # Load XML data
        result = await loader.load_data_content(simple_xml_data, 'base')
        
        # Verify that XML ID lookup was called for the reference
        assert 'test_category' in lookup_calls
        
        # Verify group creation was attempted with resolved reference
        group_model.create.assert_called()
        
        # Get the values passed to group creation
        create_call = group_model.create.call_args
        group_values = create_call[0][0] if create_call else {}
        
        # The category_id should have been resolved to the actual ID
        # (This depends on the field processing implementation)
        assert 'category_id' in group_values or len(group_values) > 0

    @pytest.mark.asyncio
    async def test_environment_ref_integration(self):
        """Test Environment.ref() method integration"""
        
        # Mock environment
        env = MagicMock(spec=Environment)
        
        # Mock ir.model.data
        ir_model_data = AsyncMock()
        ir_model_data.xmlid_lookup = AsyncMock(return_value={
            'model': 'ir.module.category',
            'res_id': 'category_123'
        })
        
        # Mock category model
        category_model = AsyncMock()
        category_record = MagicMock()
        category_record.id = 'category_123'
        category_record.name = 'Test Category'
        category_model.browse = AsyncMock(return_value=category_record)
        
        # Setup model access
        def get_model(model_name):
            if model_name == 'ir.model.data':
                return ir_model_data
            elif model_name == 'ir.module.category':
                return category_model
            else:
                raise KeyError(f"Model {model_name} not found")
        
        env.__getitem__ = get_model
        
        # Import and patch the actual ref method
        from erp.environment.core import Environment as RealEnvironment
        
        # Create a real environment instance but with mocked models
        real_env = RealEnvironment.__new__(RealEnvironment)
        real_env.__dict__.update(env.__dict__)
        real_env._logger = MagicMock()
        
        # Test the ref method
        result = await real_env.ref('base.test_category')
        
        # Verify the lookup was called
        ir_model_data.xmlid_lookup.assert_called_with('base.test_category')
        
        # Verify browse was called
        category_model.browse.assert_called_with('category_123')
        
        # Verify result
        assert result == category_record

    def test_ir_model_data_constraints(self):
        """Test ir.model.data model constraints and fields"""
        from addons.base.models.ir_model_data import IrModelData
        
        # Verify model name
        assert IrModelData._name == 'ir.model.data'
        
        # Verify description
        assert IrModelData._description == 'Model Data'
        
        # Verify constraints
        assert hasattr(IrModelData, '_sql_constraints')
        constraints = IrModelData._sql_constraints
        assert len(constraints) > 0
        
        # Check for unique constraint
        unique_constraint = next((c for c in constraints if 'unique' in c[0].lower()), None)
        assert unique_constraint is not None
        assert 'module' in unique_constraint[1]
        assert 'name' in unique_constraint[1]

    @pytest.mark.asyncio
    async def test_error_handling_in_xml_loading(self):
        """Test error handling during XML loading"""
        
        # Mock environment with failing model
        env = MagicMock(spec=Environment)
        
        # Mock ir.model.data
        ir_model_data = AsyncMock()
        ir_model_data.xmlid_lookup = AsyncMock(return_value=None)
        ir_model_data.create_or_update_xmlid = AsyncMock()
        
        # Mock failing model
        failing_model = AsyncMock()
        failing_model.create = AsyncMock(side_effect=Exception("Database error"))
        
        # Setup model access
        def get_model(model_name):
            if model_name == 'ir.model.data':
                return ir_model_data
            elif model_name == 'ir.module.category':
                return failing_model
            else:
                raise KeyError(f"Model {model_name} not found")
        
        env.__getitem__ = get_model
        
        # Create data loader
        loader = DataLoader(env)
        
        # Test with XML that will fail
        xml_data = '''<?xml version="1.0" encoding="utf-8"?>
<data>
    <record id="failing_record" model="ir.module.category">
        <field name="name">This will fail</field>
    </record>
</data>'''
        
        # Load XML data - should handle errors gracefully
        result = await loader.load_data_content(xml_data, 'base')
        
        # Verify error handling
        assert 'errors' in result
        assert isinstance(result['errors'], list)
